"use client"

import * as React from "react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "@/lib/theme-context"
import { THEME_CONFIG, getThemeConfig } from "@/lib/theme-config"

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  const currentThemeConfig = getThemeConfig(theme)
  const Icon = currentThemeConfig.icon

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          title={`${currentThemeConfig.label} тема`}
          className="h-8 w-8 p-0"
        >
          <Icon className="h-4 w-4" />
          <span className="sr-only">{currentThemeConfig.label} тема</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {Object.values(THEME_CONFIG).map((config) => {
          const ThemeIcon = config.icon
          return (
            <DropdownMenuItem
              key={config.value}
              onClick={() => setTheme(config.value)}
            >
              <ThemeIcon className="mr-2 h-4 w-4" />
              <span>{config.label}</span>
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
