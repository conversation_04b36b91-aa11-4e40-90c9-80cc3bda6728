import { useState } from "react"
import { Search, Hash, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"

interface SearchAndFilterProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  selectedTags: string[]
  onTagAdd: (tag: string) => void
  onTagRemove: (tag: string) => void
  availableTags: string[]
}

export function SearchAndFilter({
  searchQuery,
  onSearchChange,
  selectedTags,
  onTagAdd,
  onTagRemove,
  availableTags
}: SearchAndFilterProps) {
  const [showTagSuggestions, setShowTagSuggestions] = useState(false)

  const suggestedTags = availableTags
    .filter((tag) => 
      tag.toLowerCase().includes(searchQuery.toLowerCase()) && 
      !selectedTags.includes(tag)
    )
    .slice(0, 5)

  const handleSearchChange = (query: string) => {
    onSearchChange(query)
    setShowTagSuggestions(query.length > 0)
  }

  const handleTagAdd = (tag: string) => {
    onTagAdd(tag)
    onSearchChange("")
    setShowTagSuggestions(false)
  }

  return (
    <div className="space-y-4">
      {/* Поиск */}
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Поиск заметок или введите тег..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
            onFocus={() => setShowTagSuggestions(searchQuery.length > 0)}
          />
        </div>

        {/* Предложения тегов */}
        {showTagSuggestions && suggestedTags.length > 0 && (
          <div className="absolute top-full mt-1 w-full z-10 bg-card text-card-foreground rounded-xl border shadow-sm py-2">
            <div className="px-2">
              <div className="space-y-1">
                {suggestedTags.map((tag) => (
                  <Button
                    key={tag}
                    variant="ghost"
                    className="w-full justify-start h-8 px-3"
                    onClick={() => handleTagAdd(tag)}
                  >
                    <Hash className="h-3 w-3 mr-2" />
                    {tag}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Выбранные теги */}
      {selectedTags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedTags.map((tag) => (
            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
              <Hash className="h-3 w-3" />
              {tag}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onTagRemove(tag)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
