import { useState, useEffect } from 'react'

export function useTags() {
  const [tags, setTags] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadTags = async () => {
    try {
      setError(null)
      const response = await fetch('/api/tags')
      if (response.ok) {
        const data = await response.json()
        setTags(data.tags)
      } else {
        setError('Не удалось загрузить теги')
      }
    } catch (err) {
      setError('Ошибка при загрузке тегов')
    }
  }

  useEffect(() => {
    loadTags().finally(() => setLoading(false))
  }, [])

  return {
    tags,
    loading,
    error,
    refetch: loadTags
  }
}
