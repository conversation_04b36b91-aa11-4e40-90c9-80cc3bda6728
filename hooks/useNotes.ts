import { useState, useEffect } from 'react'
import { Note, CreateNoteRequest } from '@/types/notes'

export function useNotes() {
  const [notes, setNotes] = useState<Note[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadNotes = async () => {
    try {
      setError(null)
      const response = await fetch('/api/notes')
      if (response.ok) {
        const data = await response.json()
        setNotes(data.notes)
      } else {
        setError('Не удалось загрузить заметки')
      }
    } catch (err) {
      setError('Ошибка при загрузке заметок')
    }
  }

  const createNote = async (noteData: CreateNoteRequest): Promise<Note | null> => {
    try {
      setError(null)
      const response = await fetch('/api/notes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(noteData),
      })

      if (response.ok) {
        const data = await response.json()
        const newNote = data.note
        setNotes(prev => [newNote, ...prev])
        return newNote
      } else {
        setError('Не удалось создать заметку')
        return null
      }
    } catch (err) {
      setError('Ошибка при создании заметки')
      return null
    }
  }

  const deleteNote = async (noteId: string): Promise<boolean> => {
    try {
      setError(null)
      const response = await fetch(`/api/notes/${noteId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setNotes(prev => prev.filter(note => note.id !== noteId))
        return true
      } else {
        setError('Не удалось удалить заметку')
        return false
      }
    } catch (err) {
      setError('Ошибка при удалении заметки')
      return false
    }
  }

  useEffect(() => {
    loadNotes().finally(() => setLoading(false))
  }, [])

  return {
    notes,
    loading,
    error,
    createNote,
    deleteNote,
    refetch: loadNotes
  }
}
