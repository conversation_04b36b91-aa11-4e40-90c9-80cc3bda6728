import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { generateSimpleTags } from '@/lib/utils/tags'
import { CreateNoteRequest, NotesResponse, CreateNoteResponse } from '@/types/notes'

export async function GET() {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: notes, error } = await supabase
      .from('notes')
      .select(`
        id,
        content,
        content_type,
        summary_ai,
        created_at,
        note_tags (
          tags (
            id,
            name
          )
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching notes:', error)
      return NextResponse.json({ error: 'Failed to fetch notes' }, { status: 500 })
    }

    const formattedNotes = notes?.map(note => ({
      id: note.id,
      content: note.content,
      content_type: note.content_type,
      summary_ai: note.summary_ai,
      created_at: note.created_at,
      tags: note.note_tags?.map((nt: any) => nt.tags.name) || []
    })) || []

    return NextResponse.json({ notes: formattedNotes } as NotesResponse)
  } catch (error) {
    console.error('Error in GET /api/notes:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { content, content_type = 'text' }: CreateNoteRequest = await request.json()

    if (!content || !content.trim()) {
      return NextResponse.json({ error: 'Content is required' }, { status: 400 })
    }

    const { data: note, error: noteError } = await supabase
      .from('notes')
      .insert({
        user_id: user.id,
        content: content.trim(),
        content_type,
        summary_ai: null
      })
      .select()
      .single()

    if (noteError) {
      console.error('Error creating note:', noteError)
      return NextResponse.json({ error: 'Failed to create note' }, { status: 500 })
    }

    const tags = generateSimpleTags(content)
    const createdTags = []

    for (const tagName of tags) {
      let { data: existingTag, error: tagSelectError } = await supabase
        .from('tags')
        .select('id')
        .eq('name', tagName)
        .single()

      if (tagSelectError && tagSelectError.code !== 'PGRST116') {
        console.error('Error checking existing tag:', tagSelectError)
        continue
      }

      let tagId: string

      if (!existingTag) {
        const { data: newTag, error: tagInsertError } = await supabase
          .from('tags')
          .insert({ name: tagName })
          .select('id')
          .single()

        if (tagInsertError) {
          console.error('Error creating tag:', tagInsertError)
          continue
        }
        tagId = newTag.id
      } else {
        tagId = existingTag.id
      }

      const { error: noteTagError } = await supabase
        .from('note_tags')
        .insert({
          note_id: note.id,
          tag_id: tagId
        })

      if (!noteTagError) {
        createdTags.push(tagName)
      }
    }

    const responseNote = {
      id: note.id,
      content: note.content,
      content_type: note.content_type,
      summary_ai: note.summary_ai,
      created_at: note.created_at,
      tags: createdTags
    }

    return NextResponse.json({ note: responseNote } as CreateNoteResponse, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/notes:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
