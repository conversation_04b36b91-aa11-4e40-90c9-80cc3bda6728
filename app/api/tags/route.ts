import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import { TagsResponse } from '@/types/notes'

export async function GET() {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: tags, error } = await supabase
      .from('tags')
      .select(`
        name,
        note_tags!inner (
          notes!inner (
            user_id
          )
        )
      `)
      .eq('note_tags.notes.user_id', user.id)

    if (error) {
      console.error('Error fetching tags:', error)
      return NextResponse.json({ error: 'Failed to fetch tags' }, { status: 500 })
    }

    const uniqueTags = [...new Set(tags?.map(tag => tag.name) || [])]

    return NextResponse.json({ tags: uniqueTags } as TagsResponse)
  } catch (error) {
    console.error('Error in GET /api/tags:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
