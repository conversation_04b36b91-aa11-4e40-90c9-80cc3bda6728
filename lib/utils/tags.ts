const STOP_WORDS = new Set([
  'и', 'в', 'на', 'с', 'по', 'для', 'от', 'до', 'из', 'к', 'о', 'об', 'при', 'за', 'под', 'над', 'через',
  'что', 'как', 'где', 'когда', 'почему', 'который', 'которая', 'которое', 'которые',
  'это', 'то', 'та', 'те', 'тот', 'эта', 'эти', 'этот',
  'а', 'но', 'или', 'да', 'нет', 'не', 'ни', 'же', 'ли', 'бы', 'уже', 'еще', 'только', 'даже',
  'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among', 'under', 'over',
  'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can',
  'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their'
])

export function generateSimpleTags(content: string): string[] {
  const cleanContent = content
    .replace(/[#*_`~\[\](){}]/g, ' ')
    .replace(/[^\w\s\u0400-\u04FF]/g, ' ')
    .toLowerCase()
    .trim()

  const words = cleanContent
    .split(/\s+/)
    .filter(word => word.length > 2 && !STOP_WORDS.has(word))
    .slice(0, 5)

  return [...new Set(words)]
}
