import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import type { Theme } from "./theme-utils"

export interface ThemeConfig {
  value: Theme
  label: string
  icon: React.ComponentType<{ className?: string }>
}

export const THEME_CONFIG: Record<Theme, ThemeConfig> = {
  light: {
    value: "light",
    label: "Светлая",
    icon: Sun,
  },
  dark: {
    value: "dark", 
    label: "Темная",
    icon: Moon,
  },
  system: {
    value: "system",
    label: "Системная",
    icon: Monitor,
  },
} as const

export const getThemeConfig = (theme: Theme): ThemeConfig => {
  return THEME_CONFIG[theme] || THEME_CONFIG.light
}
