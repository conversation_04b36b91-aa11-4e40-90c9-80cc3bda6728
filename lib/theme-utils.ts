export type Theme = "light" | "dark" | "system"

export const THEME_STORAGE_KEY = "messenger-notes-theme"

export const getSystemTheme = (): "light" | "dark" => {
  if (typeof window === "undefined") return "light"
  return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"
}

export const getStoredTheme = (): Theme => {
  if (typeof window === "undefined") return "system"
  return (localStorage.getItem(THEME_STORAGE_KEY) as Theme) || "system"
}

export const applyTheme = (theme: Theme) => {
  if (typeof window === "undefined") return
  
  const root = window.document.documentElement
  root.classList.remove("light", "dark")

  if (theme === "system") {
    const systemTheme = getSystemTheme()
    root.classList.add(systemTheme)
  } else {
    root.classList.add(theme)
  }
}

export const getThemeInitScript = () => {
  return `
    try {
      const theme = localStorage.getItem('${THEME_STORAGE_KEY}') || 'system';
      if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    } catch (_) {}
  `
}
