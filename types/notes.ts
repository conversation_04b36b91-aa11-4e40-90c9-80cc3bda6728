export interface Note {
  id: string
  content: string
  content_type: 'text' | 'file' | 'link'
  tags: string[]
  created_at: string
  summary_ai: string | null
}

export interface CreateNoteRequest {
  content: string
  content_type?: 'text' | 'file' | 'link'
}

export interface NotesResponse {
  notes: Note[]
}

export interface CreateNoteResponse {
  note: Note
}

export interface TagsResponse {
  tags: string[]
}
