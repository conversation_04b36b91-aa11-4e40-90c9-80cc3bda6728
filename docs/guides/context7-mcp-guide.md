Use Context when you need to search for documentation for a specific library or framework. 

When using Context, make sure that you keep the range of output in the range 2k to 10k based on what you think is the best.

Use the following sources to search for documentation:
- Next.js: /vercel/next.js
- Supabase: /supabase/supabase
- Tailwind CSS: /tailwindlabs/tailwindcss.com
- Shadcn UI: /shadcn-ui/ui
- React: /reactjs/react.dev
