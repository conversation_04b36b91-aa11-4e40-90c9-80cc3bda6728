# Настройка Supabase

## Шаги для настройки проекта

1. **Создайте аккаунт и проект в Supabase**
   - Перейдите на [supabase.com](https://supabase.com)
   - Создайте новый проект
   - Дождитесь инициализации проекта

2. **Получите ключи доступа**
   - В настройках проекта перейдите в Settings → API
   - Скопируйте:
     - Project URL
     - anon public key

3. **Обновите переменные окружения**
   - Откройте файл `.env.local`
   - Замените значения:
     ```
     NEXT_PUBLIC_SUPABASE_URL=ваш_project_url
     NEXT_PUBLIC_SUPABASE_ANON_KEY=ваш_anon_key
     ```

4. **Выполните миграцию базы данных**
   - В Supabase Dashboard перейдите в SQL Editor
   - Скопируйте содержимое файла `supabase/migrations/001_create_tables.sql`
   - Выполните SQL-скрипт

5. **Настройте аутентификацию**
   - В Supabase Dashboard перейдите в Authentication → Providers
   - Убедитесь, что Email provider включен
   - При необходимости настройте SMTP для отправки писем

## Проверка работы

1. Запустите проект локально:
   ```bash
   pnpm dev
   ```

2. Откройте http://localhost:3000

3. Попробуйте:
   - Зарегистрировать новый аккаунт
   - Войти с созданными учетными данными
   - Выйти из системы

## Структура базы данных

- **notes** - таблица для хранения заметок
- **tags** - таблица для хранения тегов
- **note_tags** - связующая таблица для отношения многие-ко-многим

Все таблицы защищены Row Level Security (RLS) политиками.
