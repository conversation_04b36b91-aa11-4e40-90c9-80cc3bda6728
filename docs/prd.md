<context>
# Обзор
Проект представляет собой веб-сервис для ведения заметок с интерфейсом, вдохновленным "Избранным" в Telegram. Основная проблема, которую решает продукт, — это неэффективность поиска и организации информации в длинных чат-лентах. Ценность продукта заключается в интеллектуальной обработке каждой заметки (текста, файла, ссылки) с помощью большой языковой модели (LLM). ИИ автоматически присваивает заметкам теги и создает краткое описание для внутреннего использования, что позволяет реализовать мощный и интуитивный поиск. Сервис предназначен для широкой аудитории пользователей, которым удобен формат чата для заметок, но не хватает инструментов для их последующего анализа и поиска.

# Ключевые функции
- **Создание заметок в формате чата**
    - **Что делает:** Позволяет пользователю добавлять текстовые заметки, ссылки и файлы в интерфейс, похожий на чат.
    - **Почему это важно:** Это привычный и не требующий менеджмента способ сохранения информации для многих пользователей.
    - **Как работает:** Пользователь вводит текст или прикрепляет файл в поле ввода и отправляет его. Заметка появляется в общей ленте, отсортированной по времени добавления.

- **Поддержка длинных заметок и Markdown**
    - **Что делает:** Позволяет создавать и сохранять длинные текстовые заметки как единое целое, без разбиения на несколько сообщений. Поддерживается базовый синтаксис Markdown для форматирования текста.
    - **Почему это важно:** Решает проблему неудобства работы с большими объемами текста в стандартных мессенджерах.
    - **Как работает:** Пользователь вводит текст в единое поле ввода. При сохранении текст не делится на части и отображается как одна заметка.

- **Автоматическая обработка заметок с помощью ИИ**
    - **Что делает:** Каждая новая заметка (включая текст, PDF и изображения) отправляется на анализ мультимодальной LLM. ИИ генерирует набор релевантных тегов и краткое описание (саммари).
    - **Почему это важно:** Это ядро продукта, которое обеспечивает возможность умного поиска и автоматической организации контента без усилий со стороны пользователя.
    - **Как работает:** После добавления заметки система вызывает API-функцию, которая передает контент заметки в LLM. Полученные теги и описание сохраняются в базе данных вместе с заметкой.

- **Редактирование тегов**
    - **Что делает:** Пользователь может видеть сгенерированные ИИ теги под каждой заметкой и изменять их: удалять ненужные или добавлять свои.
    - **Почему это важно:** Дает пользователю контроль над организацией контента и позволяет скорректировать работу ИИ.
    - **Как работает:** Под заметкой отображается список тегов. Пользователь может кликнуть на тег, чтобы удалить его, или воспользоваться специальным полем для добавления нового тега.

- **Гибридный поиск**
    - **Что делает:** Предоставляет единую строку поиска с двумя режимами работы.
    - **Почему это важно:** Сочетает простоту фильтрации по тегам и мощь семантического поиска на естественном языке.
    - **Как работает:**
        1.  **Поиск по тегам:** Когда пользователь начинает вводить текст в строку поиска, система предлагает релевантные теги из его заметок. Клик по тегу мгновенно фильтрует ленту.
        2.  **Поиск с помощью ИИ:** Пользователь вводит запрос на естественном языке (например, "рецепты выпечки за прошлую весну") и нажимает Enter. ИИ анализирует запрос и, используя сохраненные саммари и теги, находит и отображает наиболее релевантные *оригинальные заметки* пользователя.

# Пользовательский опыт (UX)
- **Персоны пользователей:** Широкая аудитория. Основной сценарий использования ориентирован на любого человека, который пользуется мессенджерами для сохранения личной информации и сталкивается с трудностями ее поиска. На первом этапе основной пользователь — сам разработчик.
- **Ключевые сценарии использования (user flows):**
    1.  **Регистрация:** Новый пользователь заходит на сайт и регистрируется с помощью email и пароля.
    2.  **Создание заметки:** Пользователь вводит текст в поле ввода, нажимает "Отправить". Заметка появляется в ленте. ИИ в фоновом режиме обрабатывает ее, и через несколько секунд под заметкой появляются теги.
    3.  **Поиск заметки:** Пользователь вводит в строку поиска "рецепт пирога". Система предлагает теги `#рецепты`, `#выпечка`. Пользователь игнорирует их, дописывает "рецепт яблочного пирога от бабушки" и нажимает Enter. Система показывает 1-3 наиболее релевантные заметки, найденные ИИ.
- **Соображения по UI/UX:** Интерфейс должен быть минималистичным и интуитивно понятным, чтобы не отвлекать от основной функции — быстрого сохранения и поиска информации. Основные элементы — лента заметок и строка поиска. Использование **shadcn/ui** обеспечит чистый, современный и доступный дизайн.
</context>
<PRD>
# Техническая архитектура
- **Компоненты системы и стек:**
    - **Фреймворк:** **Next.js** (для фронтенда и API-логики).
    - **Стилизация:** **Tailwind CSS** для быстрой и консистентной разработки интерфейса по принципу utility-first.
    - **UI-компоненты:** **shadcn/ui** для создания базовых, кастомизируемых и доступных компонентов (кнопки, поля ввода, карточки, модальные окна), которые копируются непосредственно в проект.
    - **Рендеринг Markdown:** Библиотека **react-markdown** с плагином **remark-gfm** для корректного и безопасного отображения форматированного текста. Для редактирования будет использован редактор **@uiw/react-md-editor** для лучшего пользовательского опыта.
    - **База данных:** **Supabase** (PostgreSQL) для хранения пользователей, заметок, тегов и сгенерированных ИИ описаний.
    - **Аутентификация:** **Supabase Auth** для управления пользователями (регистрация/вход по email и паролю).
    - **Хранилище файлов:** **Supabase Storage** для загрузки и хранения файлов (PDF, изображения).
- **Инструменты разработки:**
    - **Менеджер пакетов:** **pnpm** будет использоваться для управления зависимостями проекта, обеспечивая высокую скорость установки и эффективное использование дискового пространства.
- **Модели данных:**
    - `users`: Стандартная модель пользователя от Supabase.
    - `notes`: (id, user_id, content_type, content, summary_ai, created_at).
    - `tags`: (id, name).
    - `note_tags`: (note_id, tag_id).
- **API и интеграции:**
    - **Vercel AI SDK:** Будет использоваться для интеграции с LLM. Это позволит гибко переключаться между различными моделями (например, от OpenAI, Google и др.).
    - **Мультимодальная LLM:** Для анализа контента будет выбрана модель, поддерживающая обработку текста, PDF и изображений (например, GPT-4o).
- **Требования к инфраструктуре:** Развертывание проекта на платформе Vercel для нативной интеграции с Next.js и Vercel AI SDK.

# План разработки
- **Требования к MVP (минимально жизнеспособному продукту):**
    1.  **Фундамент:** Настройка проекта на Next.js с использованием **pnpm**, интеграция с Supabase, настройка Tailwind CSS и установка первых компонентов из **shadcn/ui**. Реализация системы регистрации и входа.
    2.  **Создание и отображение заметок:** Реализация интерфейса чата. Использование компонентов из **shadcn/ui** (Card, Input, Button) значительно ускорит этот этап. Интеграция редактора Markdown.
    3.  **Интеграция ИИ:** Создание серверной функции (API route в Next.js), которая через Vercel AI SDK отправляет текст новой заметки в LLM и сохраняет полученные теги и саммари в БД.
    4.  **Управление тегами:** Отображение тегов под заметкой. Реализация функционала добавления/удаления тегов.
    5.  **Реализация поиска:** Создание компонента поиска. Реализация логики предложения тегов и фильтрации. Реализация логики отправки запроса к ИИ и отображения найденных заметок.
    6.  **Поддержка файлов:** Добавление возможности загрузки изображений и PDF в Supabase Storage и их передача в мультимодальную LLM для анализа.

- **Будущие улучшения:**
    - Расширенная поддержка форматов файлов.
    - Более сложные ответы от ИИ (например, генерация ответа на основе нескольких заметок с цитированием источников).
    - Внедрение альтернативных способов входа (Google, Telegram).
    - Оптимизация затрат на использование API LLM.

# Логическая последовательность зависимостей
1.  **Настройка окружения и аутентификация:** Это фундамент, без которого невозможна работа с пользовательскими данными. (Next.js + pnpm + Tailwind + shadcn/ui + Supabase).
2.  **Базовый CRUD для заметок:** Сначала нужно реализовать возможность создавать, читать и отображать простые текстовые заметки. Это позволит сразу получить видимый и работающий интерфейс.
3.  **Интеграция ИИ для анализа:** После того как заметки можно создавать, подключается ИИ для их обогащения метаданными (теги, саммари). Эта функция может работать асинхронно и не блокирует основной пользовательский опыт.
4.  **Реализация поиска:** Поиск строится поверх данных, сгенерированных на предыдущем шаге. Сначала реализуется простой поиск по тегам, затем — более сложный семантический поиск через ИИ.
5.  **Поддержка файлов:** Эта функция является расширением базового CRUD и логики анализа ИИ, поэтому реализуется после того, как отлажена работа с текстом. Каждая функция (создание, анализ, поиск) атомарна и может дорабатываться независимо.

# Риски и их минимизация
- **Технические сложности:** Анализ содержимого файлов (PDF, изображений) может быть сложным.
    - **Минимизация:** Начать с поддержки только текста. Для файлов использовать готовые мультимодальные модели, приняв их ограничения как данность для MVP.
- **Определение MVP:** Риск включить в первую версию слишком много функций.
    - **Минимизация:** План разработки четко сфокусирован на ключевой цепочке: "создал заметку -> ИИ обработал -> нашел заметку через умный поиск". Все остальные функции (например, сложные настройки, разные темы) вынесены за рамки MVP.
- **Зависимость от внешних сервисов:** Проект зависит от Supabase и провайдера LLM.
    - **Минимизация:** Для MVP это приемлемый риск, который значительно ускоряет разработку. Использование Vercel AI SDK снижает "привязку" к конкретной LLM.

# Приложение
- **Технологический стек:** **Next.js**, **Tailwind CSS**, **shadcn/ui**, **Supabase**, **Vercel AI SDK**, **react-markdown**, **pnpm**.
- **Выбор LLM:** Гибкий, с первоначальным фокусом на мультимодальную модель (например, GPT-4o или аналоги от Google).
</PRD>
